{"rustc": 12488743700189009532, "features": "[\"Win32\", \"Win32_Devices\", \"Win32_Devices_HumanInterfaceDevice\", \"Win32_Foundation\", \"Win32_Globalization\", \"Win32_Graphics\", \"Win32_Graphics_Dwm\", \"Win32_Graphics_Gdi\", \"Win32_System\", \"Win32_System_Com\", \"Win32_System_Com_StructuredStorage\", \"Win32_System_DataExchange\", \"Win32_System_Diagnostics\", \"Win32_System_Diagnostics_Debug\", \"Win32_System_LibraryLoader\", \"Win32_System_Memory\", \"Win32_System_Ole\", \"Win32_System_Registry\", \"Win32_System_SystemInformation\", \"Win32_System_SystemServices\", \"Win32_System_Threading\", \"Win32_System_Variant\", \"Win32_System_WinRT\", \"Win32_System_WindowsProgramming\", \"Win32_UI\", \"Win32_UI_Accessibility\", \"Win32_UI_Controls\", \"Win32_UI_HiDpi\", \"Win32_UI_Input\", \"Win32_UI_Input_Ime\", \"Win32_UI_Input_KeyboardAndMouse\", \"Win32_UI_Input_Pointer\", \"Win32_UI_Input_Touch\", \"Win32_UI_Shell\", \"Win32_UI_Shell_Common\", \"Win32_UI_TextServices\", \"Win32_UI_WindowsAndMessaging\", \"default\", \"std\"]", "declared_features": "[\"AI\", \"AI_MachineLearning\", \"ApplicationModel\", \"ApplicationModel_Activation\", \"ApplicationModel_AppExtensions\", \"ApplicationModel_AppService\", \"ApplicationModel_Appointments\", \"ApplicationModel_Appointments_AppointmentsProvider\", \"ApplicationModel_Appointments_DataProvider\", \"ApplicationModel_Background\", \"ApplicationModel_Calls\", \"ApplicationModel_Calls_Background\", \"ApplicationModel_Calls_Provider\", \"ApplicationModel_Chat\", \"ApplicationModel_CommunicationBlocking\", \"ApplicationModel_Contacts\", \"ApplicationModel_Contacts_DataProvider\", \"ApplicationModel_Contacts_Provider\", \"ApplicationModel_ConversationalAgent\", \"ApplicationModel_Core\", \"ApplicationModel_DataTransfer\", \"ApplicationModel_DataTransfer_DragDrop\", \"ApplicationModel_DataTransfer_DragDrop_Core\", \"ApplicationModel_DataTransfer_ShareTarget\", \"ApplicationModel_Email\", \"ApplicationModel_Email_DataProvider\", \"ApplicationModel_ExtendedExecution\", \"ApplicationModel_ExtendedExecution_Foreground\", \"ApplicationModel_Holographic\", \"ApplicationModel_LockScreen\", \"ApplicationModel_PackageExtensions\", \"ApplicationModel_Payments\", \"ApplicationModel_Payments_Provider\", \"ApplicationModel_Preview\", \"ApplicationModel_Preview_Holographic\", \"ApplicationModel_Preview_InkWorkspace\", \"ApplicationModel_Preview_Notes\", \"ApplicationModel_Resources\", \"ApplicationModel_Resources_Core\", \"ApplicationModel_Resources_Management\", \"ApplicationModel_Search\", \"ApplicationModel_Search_Core\", \"ApplicationModel_UserActivities\", \"ApplicationModel_UserActivities_Core\", \"ApplicationModel_UserDataAccounts\", \"ApplicationModel_UserDataAccounts_Provider\", \"ApplicationModel_UserDataAccounts_SystemAccess\", \"ApplicationModel_UserDataTasks\", \"ApplicationModel_UserDataTasks_DataProvider\", \"ApplicationModel_VoiceCommands\", \"ApplicationModel_Wallet\", \"ApplicationModel_Wallet_System\", \"Data\", \"Data_Html\", \"Data_Json\", \"Data_Pdf\", \"Data_Text\", \"Data_Xml\", \"Data_Xml_Dom\", \"Data_Xml_Xsl\", \"Devices\", \"Devices_Adc\", \"Devices_Adc_Provider\", \"Devices_Background\", \"Devices_Bluetooth\", \"Devices_Bluetooth_Advertisement\", \"Devices_Bluetooth_Background\", \"Devices_Bluetooth_GenericAttributeProfile\", \"Devices_Bluetooth_Rfcomm\", \"Devices_Custom\", \"Devices_Display\", \"Devices_Display_Core\", \"Devices_Enumeration\", \"Devices_Enumeration_Pnp\", \"Devices_Geolocation\", \"Devices_Geolocation_Geofencing\", \"Devices_Geolocation_Provider\", \"Devices_Gpio\", \"Devices_Gpio_Provider\", \"Devices_Haptics\", \"Devices_HumanInterfaceDevice\", \"Devices_I2c\", \"Devices_I2c_Provider\", \"Devices_Input\", \"Devices_Input_Preview\", \"Devices_Lights\", \"Devices_Lights_Effects\", \"Devices_Midi\", \"Devices_PointOfService\", \"Devices_PointOfService_Provider\", \"Devices_Portable\", \"Devices_Power\", \"Devices_Printers\", \"Devices_Printers_Extensions\", \"Devices_Pwm\", \"Devices_Pwm_Provider\", \"Devices_Radios\", \"Devices_Scanners\", \"Devices_Sensors\", \"Devices_Sensors_Custom\", \"Devices_SerialCommunication\", \"Devices_SmartCards\", \"Devices_Sms\", \"Devices_Spi\", \"Devices_Spi_Provider\", \"Devices_Usb\", \"Devices_WiFi\", \"Devices_WiFiDirect\", \"Devices_WiFiDirect_Services\", \"Embedded\", \"Embedded_DeviceLockdown\", \"Foundation\", \"Foundation_Collections\", \"Foundation_Diagnostics\", \"Foundation_Metadata\", \"Foundation_Numerics\", \"Gaming\", \"Gaming_Input\", \"Gaming_Input_Custom\", \"Gaming_Input_ForceFeedback\", \"Gaming_Input_Preview\", \"Gaming_Preview\", \"Gaming_Preview_GamesEnumeration\", \"Gaming_UI\", \"Gaming_XboxLive\", \"Gaming_XboxLive_Storage\", \"Globalization\", \"Globalization_Collation\", \"Globalization_DateTimeFormatting\", \"Globalization_Fonts\", \"Globalization_NumberFormatting\", \"Globalization_PhoneNumberFormatting\", \"Graphics\", \"Graphics_Capture\", \"Graphics_DirectX\", \"Graphics_DirectX_Direct3D11\", \"Graphics_Display\", \"Graphics_Display_Core\", \"Graphics_Effects\", \"Graphics_Holographic\", \"Graphics_Imaging\", \"Graphics_Printing\", \"Graphics_Printing3D\", \"Graphics_Printing_OptionDetails\", \"Graphics_Printing_PrintSupport\", \"Graphics_Printing_PrintTicket\", \"Graphics_Printing_Workflow\", \"Management\", \"Management_Core\", \"Management_Deployment\", \"Management_Deployment_Preview\", \"Management_Policies\", \"Management_Setup\", \"Management_Update\", \"Management_Workplace\", \"Media\", \"Media_AppBroadcasting\", \"Media_AppRecording\", \"Media_Audio\", \"Media_Capture\", \"Media_Capture_Core\", \"Media_Capture_Frames\", \"Media_Casting\", \"Media_ClosedCaptioning\", \"Media_ContentRestrictions\", \"Media_Control\", \"Media_Core\", \"Media_Core_Preview\", \"Media_Devices\", \"Media_Devices_Core\", \"Media_DialProtocol\", \"Media_Editing\", \"Media_Effects\", \"Media_FaceAnalysis\", \"Media_Import\", \"Media_MediaProperties\", \"Media_Miracast\", \"Media_Ocr\", \"Media_PlayTo\", \"Media_Playback\", \"Media_Playlists\", \"Media_Protection\", \"Media_Protection_PlayReady\", \"Media_Render\", \"Media_SpeechRecognition\", \"Media_SpeechSynthesis\", \"Media_Streaming\", \"Media_Streaming_Adaptive\", \"Media_Transcoding\", \"Networking\", \"Networking_BackgroundTransfer\", \"Networking_Connectivity\", \"Networking_NetworkOperators\", \"Networking_Proximity\", \"Networking_PushNotifications\", \"Networking_ServiceDiscovery\", \"Networking_ServiceDiscovery_Dnssd\", \"Networking_Sockets\", \"Networking_Vpn\", \"Networking_XboxLive\", \"Perception\", \"Perception_Automation\", \"Perception_Automation_Core\", \"Perception_People\", \"Perception_Spatial\", \"Perception_Spatial_Preview\", \"Perception_Spatial_Surfaces\", \"Phone\", \"Phone_ApplicationModel\", \"Phone_Devices\", \"Phone_Devices_Notification\", \"Phone_Devices_Power\", \"Phone_Management\", \"Phone_Management_Deployment\", \"Phone_Media\", \"Phone_Media_Devices\", \"Phone_Notification\", \"Phone_Notification_Management\", \"Phone_PersonalInformation\", \"Phone_PersonalInformation_Provisioning\", \"Phone_Speech\", \"Phone_Speech_Recognition\", \"Phone_StartScreen\", \"Phone_System\", \"Phone_System_Power\", \"Phone_System_Profile\", \"Phone_System_UserProfile\", \"Phone_System_UserProfile_GameServices\", \"Phone_System_UserProfile_GameServices_Core\", \"Phone_UI\", \"Phone_UI_Input\", \"Security\", \"Security_Authentication\", \"Security_Authentication_Identity\", \"Security_Authentication_Identity_Core\", \"Security_Authentication_OnlineId\", \"Security_Authentication_Web\", \"Security_Authentication_Web_Core\", \"Security_Authentication_Web_Provider\", \"Security_Authorization\", \"Security_Authorization_AppCapabilityAccess\", \"Security_Credentials\", \"Security_Credentials_UI\", \"Security_Cryptography\", \"Security_Cryptography_Certificates\", \"Security_Cryptography_Core\", \"Security_Cryptography_DataProtection\", \"Security_DataProtection\", \"Security_EnterpriseData\", \"Security_ExchangeActiveSyncProvisioning\", \"Security_Isolation\", \"Services\", \"Services_Maps\", \"Services_Maps_Guidance\", \"Services_Maps_LocalSearch\", \"Services_Maps_OfflineMaps\", \"Services_Store\", \"Services_TargetedContent\", \"Storage\", \"Storage_AccessCache\", \"Storage_BulkAccess\", \"Storage_Compression\", \"Storage_FileProperties\", \"Storage_Pickers\", \"Storage_Pickers_Provider\", \"Storage_Provider\", \"Storage_Search\", \"Storage_Streams\", \"System\", \"System_Diagnostics\", \"System_Diagnostics_DevicePortal\", \"System_Diagnostics_Telemetry\", \"System_Diagnostics_TraceReporting\", \"System_Display\", \"System_Implementation\", \"System_Implementation_FileExplorer\", \"System_Inventory\", \"System_Power\", \"System_Profile\", \"System_Profile_SystemManufacturers\", \"System_RemoteDesktop\", \"System_RemoteDesktop_Input\", \"System_RemoteDesktop_Provider\", \"System_RemoteSystems\", \"System_Threading\", \"System_Threading_Core\", \"System_Update\", \"System_UserProfile\", \"UI\", \"UI_Accessibility\", \"UI_ApplicationSettings\", \"UI_Composition\", \"UI_Composition_Core\", \"UI_Composition_Desktop\", \"UI_Composition_Diagnostics\", \"UI_Composition_Effects\", \"UI_Composition_Interactions\", \"UI_Composition_Scenes\", \"UI_Core\", \"UI_Core_AnimationMetrics\", \"UI_Core_Preview\", \"UI_Input\", \"UI_Input_Core\", \"UI_Input_Inking\", \"UI_Input_Inking_Analysis\", \"UI_Input_Inking_Core\", \"UI_Input_Inking_Preview\", \"UI_Input_Preview\", \"UI_Input_Preview_Injection\", \"UI_Input_Spatial\", \"UI_Notifications\", \"UI_Notifications_Management\", \"UI_Notifications_Preview\", \"UI_Popups\", \"UI_Shell\", \"UI_StartScreen\", \"UI_Text\", \"UI_Text_Core\", \"UI_UIAutomation\", \"UI_UIAutomation_Core\", \"UI_ViewManagement\", \"UI_ViewManagement_Core\", \"UI_WebUI\", \"UI_WebUI_Core\", \"UI_WindowManagement\", \"UI_WindowManagement_Preview\", \"Wdk\", \"Wdk_Devices\", \"Wdk_Devices_Bluetooth\", \"Wdk_Devices_HumanInterfaceDevice\", \"Wdk_Foundation\", \"Wdk_Graphics\", \"Wdk_Graphics_Direct3D\", \"Wdk_NetworkManagement\", \"Wdk_NetworkManagement_Ndis\", \"Wdk_NetworkManagement_WindowsFilteringPlatform\", \"Wdk_Storage\", \"Wdk_Storage_FileSystem\", \"Wdk_Storage_FileSystem_Minifilters\", \"Wdk_System\", \"Wdk_System_IO\", \"Wdk_System_Memory\", \"Wdk_System_OfflineRegistry\", \"Wdk_System_Registry\", \"Wdk_System_SystemInformation\", \"Wdk_System_SystemServices\", \"Wdk_System_Threading\", \"Web\", \"Web_AtomPub\", \"Web_Http\", \"Web_Http_Diagnostics\", \"Web_Http_Filters\", \"Web_Http_Headers\", \"Web_Syndication\", \"Web_UI\", \"Web_UI_Interop\", \"Win32\", \"Win32_AI\", \"Win32_AI_MachineLearning\", \"Win32_AI_MachineLearning_DirectML\", \"Win32_AI_MachineLearning_WinML\", \"Win32_Data\", \"Win32_Data_HtmlHelp\", \"Win32_Data_RightsManagement\", \"Win32_Data_Xml\", \"Win32_Data_Xml_MsXml\", \"Win32_Data_Xml_XmlLite\", \"Win32_Devices\", \"Win32_Devices_AllJoyn\", \"Win32_Devices_Beep\", \"Win32_Devices_BiometricFramework\", \"Win32_Devices_Bluetooth\", \"Win32_Devices_Cdrom\", \"Win32_Devices_Communication\", \"Win32_Devices_DeviceAccess\", \"Win32_Devices_DeviceAndDriverInstallation\", \"Win32_Devices_DeviceQuery\", \"Win32_Devices_Display\", \"Win32_Devices_Dvd\", \"Win32_Devices_Enumeration\", \"Win32_Devices_Enumeration_Pnp\", \"Win32_Devices_Fax\", \"Win32_Devices_FunctionDiscovery\", \"Win32_Devices_Geolocation\", \"Win32_Devices_HumanInterfaceDevice\", \"Win32_Devices_ImageAcquisition\", \"Win32_Devices_Nfc\", \"Win32_Devices_Nfp\", \"Win32_Devices_PortableDevices\", \"Win32_Devices_Properties\", \"Win32_Devices_Pwm\", \"Win32_Devices_Sensors\", \"Win32_Devices_SerialCommunication\", \"Win32_Devices_Tapi\", \"Win32_Devices_Usb\", \"Win32_Devices_WebServicesOnDevices\", \"Win32_Foundation\", \"Win32_Gaming\", \"Win32_Globalization\", \"Win32_Graphics\", \"Win32_Graphics_CompositionSwapchain\", \"Win32_Graphics_DXCore\", \"Win32_Graphics_Direct2D\", \"Win32_Graphics_Direct2D_Common\", \"Win32_Graphics_Direct3D\", \"Win32_Graphics_Direct3D10\", \"Win32_Graphics_Direct3D11\", \"Win32_Graphics_Direct3D11on12\", \"Win32_Graphics_Direct3D12\", \"Win32_Graphics_Direct3D9\", \"Win32_Graphics_Direct3D9on12\", \"Win32_Graphics_Direct3D_Dxc\", \"Win32_Graphics_Direct3D_Fxc\", \"Win32_Graphics_DirectComposition\", \"Win32_Graphics_DirectDraw\", \"Win32_Graphics_DirectManipulation\", \"Win32_Graphics_DirectWrite\", \"Win32_Graphics_Dwm\", \"Win32_Graphics_Dxgi\", \"Win32_Graphics_Dxgi_Common\", \"Win32_Graphics_Gdi\", \"Win32_Graphics_GdiPlus\", \"Win32_Graphics_Hlsl\", \"Win32_Graphics_Imaging\", \"Win32_Graphics_Imaging_D2D\", \"Win32_Graphics_OpenGL\", \"Win32_Graphics_Printing\", \"Win32_Graphics_Printing_PrintTicket\", \"Win32_Management\", \"Win32_Management_MobileDeviceManagementRegistration\", \"Win32_Media\", \"Win32_Media_Audio\", \"Win32_Media_Audio_Apo\", \"Win32_Media_Audio_DirectMusic\", \"Win32_Media_Audio_DirectSound\", \"Win32_Media_Audio_Endpoints\", \"Win32_Media_Audio_XAudio2\", \"Win32_Media_DeviceManager\", \"Win32_Media_DirectShow\", \"Win32_Media_DirectShow_Tv\", \"Win32_Media_DirectShow_Xml\", \"Win32_Media_DxMediaObjects\", \"Win32_Media_KernelStreaming\", \"Win32_Media_LibrarySharingServices\", \"Win32_Media_MediaFoundation\", \"Win32_Media_MediaPlayer\", \"Win32_Media_Multimedia\", \"Win32_Media_PictureAcquisition\", \"Win32_Media_Speech\", \"Win32_Media_Streaming\", \"Win32_Media_WindowsMediaFormat\", \"Win32_NetworkManagement\", \"Win32_NetworkManagement_Dhcp\", \"Win32_NetworkManagement_Dns\", \"Win32_NetworkManagement_InternetConnectionWizard\", \"Win32_NetworkManagement_IpHelper\", \"Win32_NetworkManagement_MobileBroadband\", \"Win32_NetworkManagement_Multicast\", \"Win32_NetworkManagement_Ndis\", \"Win32_NetworkManagement_NetBios\", \"Win32_NetworkManagement_NetManagement\", \"Win32_NetworkManagement_NetShell\", \"Win32_NetworkManagement_NetworkDiagnosticsFramework\", \"Win32_NetworkManagement_NetworkPolicyServer\", \"Win32_NetworkManagement_P2P\", \"Win32_NetworkManagement_QoS\", \"Win32_NetworkManagement_Rras\", \"Win32_NetworkManagement_Snmp\", \"Win32_NetworkManagement_WNet\", \"Win32_NetworkManagement_WebDav\", \"Win32_NetworkManagement_WiFi\", \"Win32_NetworkManagement_WindowsConnectNow\", \"Win32_NetworkManagement_WindowsConnectionManager\", \"Win32_NetworkManagement_WindowsFilteringPlatform\", \"Win32_NetworkManagement_WindowsFirewall\", \"Win32_NetworkManagement_WindowsNetworkVirtualization\", \"Win32_Networking\", \"Win32_Networking_ActiveDirectory\", \"Win32_Networking_BackgroundIntelligentTransferService\", \"Win32_Networking_Clustering\", \"Win32_Networking_HttpServer\", \"Win32_Networking_Ldap\", \"Win32_Networking_NetworkListManager\", \"Win32_Networking_RemoteDifferentialCompression\", \"Win32_Networking_WebSocket\", \"Win32_Networking_WinHttp\", \"Win32_Networking_WinInet\", \"Win32_Networking_WinSock\", \"Win32_Networking_WindowsWebServices\", \"Win32_Security\", \"Win32_Security_AppLocker\", \"Win32_Security_Authentication\", \"Win32_Security_Authentication_Identity\", \"Win32_Security_Authentication_Identity_Provider\", \"Win32_Security_Authorization\", \"Win32_Security_Authorization_UI\", \"Win32_Security_ConfigurationSnapin\", \"Win32_Security_Credentials\", \"Win32_Security_Cryptography\", \"Win32_Security_Cryptography_Catalog\", \"Win32_Security_Cryptography_Certificates\", \"Win32_Security_Cryptography_Sip\", \"Win32_Security_Cryptography_UI\", \"Win32_Security_DiagnosticDataQuery\", \"Win32_Security_DirectoryServices\", \"Win32_Security_EnterpriseData\", \"Win32_Security_ExtensibleAuthenticationProtocol\", \"Win32_Security_Isolation\", \"Win32_Security_LicenseProtection\", \"Win32_Security_NetworkAccessProtection\", \"Win32_Security_Tpm\", \"Win32_Security_WinTrust\", \"Win32_Security_WinWlx\", \"Win32_Storage\", \"Win32_Storage_Cabinets\", \"Win32_Storage_CloudFilters\", \"Win32_Storage_Compression\", \"Win32_Storage_DataDeduplication\", \"Win32_Storage_DistributedFileSystem\", \"Win32_Storage_EnhancedStorage\", \"Win32_Storage_FileHistory\", \"Win32_Storage_FileServerResourceManager\", \"Win32_Storage_FileSystem\", \"Win32_Storage_Imapi\", \"Win32_Storage_IndexServer\", \"Win32_Storage_InstallableFileSystems\", \"Win32_Storage_IscsiDisc\", \"Win32_Storage_Jet\", \"Win32_Storage_Nvme\", \"Win32_Storage_OfflineFiles\", \"Win32_Storage_OperationRecorder\", \"Win32_Storage_Packaging\", \"Win32_Storage_Packaging_Appx\", \"Win32_Storage_Packaging_Opc\", \"Win32_Storage_ProjectedFileSystem\", \"Win32_Storage_StructuredStorage\", \"Win32_Storage_Vhd\", \"Win32_Storage_VirtualDiskService\", \"Win32_Storage_Vss\", \"Win32_Storage_Xps\", \"Win32_Storage_Xps_Printing\", \"Win32_System\", \"Win32_System_AddressBook\", \"Win32_System_Antimalware\", \"Win32_System_ApplicationInstallationAndServicing\", \"Win32_System_ApplicationVerifier\", \"Win32_System_AssessmentTool\", \"Win32_System_ClrHosting\", \"Win32_System_Com\", \"Win32_System_Com_CallObj\", \"Win32_System_Com_ChannelCredentials\", \"Win32_System_Com_Events\", \"Win32_System_Com_Marshal\", \"Win32_System_Com_StructuredStorage\", \"Win32_System_Com_UI\", \"Win32_System_Com_Urlmon\", \"Win32_System_ComponentServices\", \"Win32_System_Console\", \"Win32_System_Contacts\", \"Win32_System_CorrelationVector\", \"Win32_System_DataExchange\", \"Win32_System_DeploymentServices\", \"Win32_System_DesktopSharing\", \"Win32_System_DeveloperLicensing\", \"Win32_System_Diagnostics\", \"Win32_System_Diagnostics_Ceip\", \"Win32_System_Diagnostics_ClrProfiling\", \"Win32_System_Diagnostics_Debug\", \"Win32_System_Diagnostics_Debug_ActiveScript\", \"Win32_System_Diagnostics_Debug_Extensions\", \"Win32_System_Diagnostics_Etw\", \"Win32_System_Diagnostics_ProcessSnapshotting\", \"Win32_System_Diagnostics_ToolHelp\", \"Win32_System_Diagnostics_TraceLogging\", \"Win32_System_DistributedTransactionCoordinator\", \"Win32_System_Environment\", \"Win32_System_ErrorReporting\", \"Win32_System_EventCollector\", \"Win32_System_EventLog\", \"Win32_System_EventNotificationService\", \"Win32_System_GroupPolicy\", \"Win32_System_HostCompute\", \"Win32_System_HostComputeNetwork\", \"Win32_System_HostComputeSystem\", \"Win32_System_Hypervisor\", \"Win32_System_IO\", \"Win32_System_Iis\", \"Win32_System_Ioctl\", \"Win32_System_JobObjects\", \"Win32_System_Js\", \"Win32_System_Kernel\", \"Win32_System_LibraryLoader\", \"Win32_System_Mailslots\", \"Win32_System_Mapi\", \"Win32_System_Memory\", \"Win32_System_Memory_NonVolatile\", \"Win32_System_MessageQueuing\", \"Win32_System_MixedReality\", \"Win32_System_Mmc\", \"Win32_System_Ole\", \"Win32_System_ParentalControls\", \"Win32_System_PasswordManagement\", \"Win32_System_Performance\", \"Win32_System_Performance_HardwareCounterProfiling\", \"Win32_System_Pipes\", \"Win32_System_Power\", \"Win32_System_ProcessStatus\", \"Win32_System_RealTimeCommunications\", \"Win32_System_Recovery\", \"Win32_System_Registry\", \"Win32_System_RemoteAssistance\", \"Win32_System_RemoteDesktop\", \"Win32_System_RemoteManagement\", \"Win32_System_RestartManager\", \"Win32_System_Restore\", \"Win32_System_Rpc\", \"Win32_System_Search\", \"Win32_System_Search_Common\", \"Win32_System_SecurityCenter\", \"Win32_System_ServerBackup\", \"Win32_System_Services\", \"Win32_System_SettingsManagementInfrastructure\", \"Win32_System_SetupAndMigration\", \"Win32_System_Shutdown\", \"Win32_System_SideShow\", \"Win32_System_StationsAndDesktops\", \"Win32_System_SubsystemForLinux\", \"Win32_System_SystemInformation\", \"Win32_System_SystemServices\", \"Win32_System_TaskScheduler\", \"Win32_System_Threading\", \"Win32_System_Time\", \"Win32_System_TpmBaseServices\", \"Win32_System_TransactionServer\", \"Win32_System_UpdateAgent\", \"Win32_System_UpdateAssessment\", \"Win32_System_UserAccessLogging\", \"Win32_System_Variant\", \"Win32_System_VirtualDosMachines\", \"Win32_System_WinRT\", \"Win32_System_WinRT_AllJoyn\", \"Win32_System_WinRT_Composition\", \"Win32_System_WinRT_CoreInputView\", \"Win32_System_WinRT_Direct3D11\", \"Win32_System_WinRT_Display\", \"Win32_System_WinRT_Graphics\", \"Win32_System_WinRT_Graphics_Capture\", \"Win32_System_WinRT_Graphics_Direct2D\", \"Win32_System_WinRT_Graphics_Imaging\", \"Win32_System_WinRT_Holographic\", \"Win32_System_WinRT_Isolation\", \"Win32_System_WinRT_ML\", \"Win32_System_WinRT_Media\", \"Win32_System_WinRT_Metadata\", \"Win32_System_WinRT_Pdf\", \"Win32_System_WinRT_Printing\", \"Win32_System_WinRT_Shell\", \"Win32_System_WinRT_Storage\", \"Win32_System_WindowsProgramming\", \"Win32_System_WindowsSync\", \"Win32_System_Wmi\", \"Win32_UI\", \"Win32_UI_Accessibility\", \"Win32_UI_Animation\", \"Win32_UI_ColorSystem\", \"Win32_UI_Controls\", \"Win32_UI_Controls_Dialogs\", \"Win32_UI_Controls_RichEdit\", \"Win32_UI_HiDpi\", \"Win32_UI_Input\", \"Win32_UI_Input_Ime\", \"Win32_UI_Input_Ink\", \"Win32_UI_Input_KeyboardAndMouse\", \"Win32_UI_Input_Pointer\", \"Win32_UI_Input_Radial\", \"Win32_UI_Input_Touch\", \"Win32_UI_Input_XboxController\", \"Win32_UI_InteractionContext\", \"Win32_UI_LegacyWindowsEnvironmentFeatures\", \"Win32_UI_Magnification\", \"Win32_UI_Notifications\", \"Win32_UI_Ribbon\", \"Win32_UI_Shell\", \"Win32_UI_Shell_Common\", \"Win32_UI_Shell_PropertiesSystem\", \"Win32_UI_TabletPC\", \"Win32_UI_TextServices\", \"Win32_UI_WindowsAndMessaging\", \"Win32_UI_Wpf\", \"Win32_Web\", \"Win32_Web_InternetExplorer\", \"default\", \"deprecated\", \"docs\", \"std\"]", "target": 12079305173176943989, "profile": 12257758352969185987, "path": 9990988833055937457, "deps": [[654630577187138008, "windows_numerics", false, 14498302045716650082], [5628259161083531273, "windows_core", false, 17464852286181123595], [11505586985402185701, "windows_link", false, 7318759136188769744], [14242725669310060391, "windows_collections", false, 5426993477656251251], [15497576709604833034, "windows_future", false, 13072838932445066870]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\windows-84a41cc8849eba35\\dep-lib-windows", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
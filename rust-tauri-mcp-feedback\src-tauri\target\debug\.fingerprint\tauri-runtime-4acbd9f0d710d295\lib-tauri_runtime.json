{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 18086563540249045028, "deps": [[654232091421095663, "tauri_utils", false, 14871906533553561277], [3150220818285335163, "url", false, 11858171219582627557], [4143744114649553716, "raw_window_handle", false, 2607158835357174031], [7606335748176206944, "dpi", false, 6478925815745228025], [8569119365930580996, "serde_json", false, 13240143706165462653], [9010263965687315507, "http", false, 2011574105913395122], [9689903380558560274, "serde", false, 18218662921607851820], [10806645703491011684, "thiserror", false, 14768882660834217619], [12943761728066819757, "build_script_build", false, 2383160265756477354], [14585479307175734061, "windows", false, 5984141677745590645], [16727543399706004146, "cookie", false, 3857875843423345041]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-4acbd9f0d710d295\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
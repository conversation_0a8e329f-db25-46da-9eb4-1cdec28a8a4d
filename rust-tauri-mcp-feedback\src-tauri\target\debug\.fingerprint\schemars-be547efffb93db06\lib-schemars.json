{"rustc": 12488743700189009532, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 11377689158968846504, "deps": [[3150220818285335163, "url", false, 5917304242900474347], [6913375703034175521, "build_script_build", false, 8014893713948595497], [6982418085031928086, "dyn_clone", false, 13674130383346719295], [8319709847752024821, "uuid1", false, 3243959443322675031], [8569119365930580996, "serde_json", false, 4997167143271460451], [9689903380558560274, "serde", false, 11206021980198572661], [14923790796823607459, "indexmap", false, 4890424237791030186], [16071897500792579091, "schemars_derive", false, 1692956068212869672]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-be547efffb93db06\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 12488743700189009532, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 17181063756798201183, "deps": [[654232091421095663, "tauri_utils", false, 3655762623775767168], [3060637413840920116, "proc_macro2", false, 10216015943829803987], [3150220818285335163, "url", false, 5917304242900474347], [4899080583175475170, "semver", false, 6686575274129190876], [4974441333307933176, "syn", false, 16366498784332135489], [7170110829644101142, "json_patch", false, 7274104630437537197], [7392050791754369441, "ico", false, 627384136271691404], [8319709847752024821, "uuid", false, 3243959443322675031], [8569119365930580996, "serde_json", false, 4997167143271460451], [9556762810601084293, "brotli", false, 12334169869696479745], [9689903380558560274, "serde", false, 11206021980198572661], [9857275760291862238, "sha2", false, 17424803800067041080], [10806645703491011684, "thiserror", false, 14768882660834217619], [12687914511023397207, "png", false, 7210839171368358466], [13077212702700853852, "base64", false, 16514948115193929530], [15622660310229662834, "walkdir", false, 15679774397957866197], [17990358020177143287, "quote", false, 9619711520884181919]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-219224d5f66ac3d1\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
["\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\available_monitors.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\center.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\close.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\create.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\current_monitor.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\cursor_position.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\destroy.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\get_all_windows.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\hide.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\inner_position.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\inner_size.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\internal_toggle_maximize.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_always_on_top.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_closable.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_decorated.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_focused.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_fullscreen.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_maximizable.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_maximized.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_minimizable.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_minimized.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_resizable.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\is_visible.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\maximize.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\minimize.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\monitor_from_point.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\outer_position.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\outer_size.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\primary_monitor.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\request_user_attention.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\scale_factor.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_bottom.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_top.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_background_color.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_badge_count.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_badge_label.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_closable.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_content_protected.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_grab.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_icon.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_position.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_visible.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_decorations.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_effects.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_focus.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_fullscreen.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_icon.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_ignore_cursor_events.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_max_size.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_maximizable.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_min_size.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_minimizable.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_overlay_icon.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_position.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_progress_bar.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_resizable.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_shadow.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_size.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_size_constraints.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_skip_taskbar.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_theme.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_title.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_title_bar_style.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\set_visible_on_all_workspaces.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\show.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\start_dragging.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\start_resize_dragging.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\theme.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\title.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\toggle_maximize.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\unmaximize.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\commands\\unminimize.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\window\\autogenerated\\default.toml"]
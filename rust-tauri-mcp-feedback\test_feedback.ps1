# Test feedback tool call
Write-Host "Testing MCP feedback tool" -ForegroundColor Green

$feedbackData = @{
    jsonrpc = "2.0"
    id = 3
    method = "tools/call"
    params = @{
        name = "feedback"
        arguments = @{
            work_summary = @"
## SSE模式测试成功！

这是通过SSE模式调用MCP Feedback Tool的测试。

### 测试结果
- ✅ 健康检查端点正常
- ✅ MCP初始化成功
- ✅ 工具列表获取成功
- ✅ Feedback工具调用测试

### 技术细节
- 传输模式: SSE (Server-Sent Events)
- 服务器地址: http://127.0.0.1:8080
- 协议版本: 2024-11-05

这证明了Rust+Tauri的MCP工具可以成功运行在SSE模式下！
"@
            title = "SSE模式测试会话"
            allow_save = $true
        }
    }
}

$json = $feedbackData | ConvertTo-Json -Depth 5
Write-Host "Sending feedback tool call..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:8080/mcp" -Method Post -Body $json -ContentType "application/json"
    Write-Host "Feedback tool response:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

{"rustc": 12488743700189009532, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 14714332040258902933, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 2377008741735888723], [3150220818285335163, "url", false, 11858171219582627557], [3191507132440681679, "serde_untagged", false, 6050913074982618009], [4071963112282141418, "serde_with", false, 8823515307050269618], [4899080583175475170, "semver", false, 8457118863915942715], [5986029879202738730, "log", false, 1541928973590822586], [6606131838865521726, "ctor", false, 10368431908899313837], [7170110829644101142, "json_patch", false, 10952096057248808733], [8319709847752024821, "uuid", false, 713660612388245101], [8569119365930580996, "serde_json", false, 13240143706165462653], [9010263965687315507, "http", false, 2011574105913395122], [9451456094439810778, "regex", false, 10661633459671320324], [9556762810601084293, "brotli", false, 12334169869696479745], [9689903380558560274, "serde", false, 18218662921607851820], [10806645703491011684, "thiserror", false, 14768882660834217619], [11989259058781683633, "dunce", false, 15281095910620167697], [13625485746686963219, "anyhow", false, 5958319405184604132], [15609422047640926750, "toml", false, 6580255504919216173], [15622660310229662834, "walkdir", false, 18050170391585580715], [15932120279885307830, "memchr", false, 9639641664727124646], [17146114186171651583, "infer", false, 9314029499728907557], [17155886227862585100, "glob", false, 12797762323271349913], [17186037756130803222, "phf", false, 12921465449136553705]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-0a9fb341d267a1ee\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9388111363236443182, "build_script_build", false, 1684102168420313], [12092653563678505622, "build_script_build", false, 14393040121744108566], [16702348383442838006, "build_script_build", false, 4174352397476003724]], "local": [{"RerunIfChanged": {"output": "debug\\build\\rust-tauri-mcp-feedback-ffc76076e6a78013\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}
# Test tools list
Write-Host "Testing MCP tools list" -ForegroundColor Green

$toolsData = @{
    jsonrpc = "2.0"
    id = 2
    method = "tools/list"
    params = @{}
}

$json = $toolsData | ConvertTo-Json -Depth 5
Write-Host "Sending tools/list request..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:8080/mcp" -Method Post -Body $json -ContentType "application/json"
    Write-Host "Tools list response:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

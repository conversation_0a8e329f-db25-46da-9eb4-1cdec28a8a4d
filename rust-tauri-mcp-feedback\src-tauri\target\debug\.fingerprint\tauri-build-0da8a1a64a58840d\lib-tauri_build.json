{"rustc": 12488743700189009532, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 5635393548781283232, "deps": [[654232091421095663, "tauri_utils", false, 3655762623775767168], [4824857623768494398, "cargo_toml", false, 408454803218023298], [4899080583175475170, "semver", false, 6686575274129190876], [6913375703034175521, "schemars", false, 11397848206601901488], [7170110829644101142, "json_patch", false, 7274104630437537197], [8569119365930580996, "serde_json", false, 4997167143271460451], [9689903380558560274, "serde", false, 11206021980198572661], [12714016054753183456, "tauri_winres", false, 11719953875903326309], [13077543566650298139, "heck", false, 9633679607406152651], [13625485746686963219, "anyhow", false, 5958319405184604132], [15609422047640926750, "toml", false, 14042350668861020453], [15622660310229662834, "walkdir", false, 15679774397957866197], [16928111194414003569, "dirs", false, 7224414717379208715], [17155886227862585100, "glob", false, 12797762323271349913]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-0da8a1a64a58840d\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 12488743700189009532, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 11987593577772629573, "path": 17784342193286557217, "deps": [[2013030631243296465, "webview2_com", false, 6644751358008741449], [3334271191048661305, "windows_version", false, 16290438646986140060], [3722963349756955755, "once_cell", false, 5781271793034976311], [4143744114649553716, "raw_window_handle", false, 2607158835357174031], [5628259161083531273, "windows_core", false, 17464852286181123595], [7606335748176206944, "dpi", false, 6478925815745228025], [9010263965687315507, "http", false, 2011574105913395122], [9141053277961803901, "build_script_build", false, 15374892713602550659], [10806645703491011684, "thiserror", false, 14768882660834217619], [11989259058781683633, "dunce", false, 15281095910620167697], [14585479307175734061, "windows", false, 5984141677745590645], [16727543399706004146, "cookie", false, 3857875843423345041]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-cbc318e498638673\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
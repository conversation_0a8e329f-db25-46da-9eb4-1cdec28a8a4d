{"rustc": 12488743700189009532, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 12091596292427574350, "deps": [[654232091421095663, "tauri_utils", false, 3655762623775767168], [2704937418414716471, "tauri_codegen", false, 2160820762051376873], [3060637413840920116, "proc_macro2", false, 10216015943829803987], [4974441333307933176, "syn", false, 16366498784332135489], [13077543566650298139, "heck", false, 9633679607406152651], [17990358020177143287, "quote", false, 9619711520884181919]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-16a71898bfda48ef\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
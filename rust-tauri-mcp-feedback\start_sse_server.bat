@echo off
echo Starting MCP Feedback Tool in SSE mode...

set MCP_TRANSPORT=sse
set MCP_HOST=127.0.0.1
set MCP_PORT=8080
set RUST_LOG=debug

echo Environment variables set:
echo MCP_TRANSPORT=%MCP_TRANSPORT%
echo MCP_HOST=%MCP_HOST%
echo MCP_PORT=%MCP_PORT%
echo RUST_LOG=%RUST_LOG%
echo.

echo Starting server on http://%MCP_HOST%:%MCP_PORT%
echo SSE endpoint: http://%MCP_HOST%:%MCP_PORT%/sse
echo MCP endpoint: http://%MCP_HOST%:%MCP_PORT%/mcp
echo Health check: http://%MCP_HOST%:%MCP_PORT%/health
echo.
echo Press Ctrl+C to stop the server
echo.

echo Executing: .\src-tauri\target\debug\mcp_server.exe
.\src-tauri\target\debug\mcp_server.exe

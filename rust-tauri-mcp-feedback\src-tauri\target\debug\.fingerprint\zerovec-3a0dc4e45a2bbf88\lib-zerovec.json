{"rustc": 12488743700189009532, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 15657897354478470176, "path": 7154753264607036506, "deps": [[9620753569207166497, "zerovec_derive", false, 2146685086047365565], [10706449961930108323, "yoke", false, 10917591334597825922], [17046516144589451410, "zerofrom", false, 14737266652324498361]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-3a0dc4e45a2bbf88\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
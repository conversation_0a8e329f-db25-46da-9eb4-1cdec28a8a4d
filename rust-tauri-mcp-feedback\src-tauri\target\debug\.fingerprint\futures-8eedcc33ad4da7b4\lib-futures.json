{"rustc": 12488743700189009532, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 2946988075690570688, "deps": [[5103565458935487, "futures_io", false, 7254799124565121960], [1811549171721445101, "futures_channel", false, 13156570865159914785], [7013762810557009322, "futures_sink", false, 16190572654118984067], [7620660491849607393, "futures_core", false, 13312733302706390815], [10629569228670356391, "futures_util", false, 2064565491164891640], [12779779637805422465, "futures_executor", false, 13349070036727647826], [16240732885093539806, "futures_task", false, 3357989597671250158]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-8eedcc33ad4da7b4\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
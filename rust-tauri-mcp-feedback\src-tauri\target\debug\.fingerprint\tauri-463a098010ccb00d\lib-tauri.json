{"rustc": 12488743700189009532, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 90208067532340823, "deps": [[40386456601120721, "percent_encoding", false, 942593547561527029], [654232091421095663, "tauri_utils", false, 14871906533553561277], [1200537532907108615, "url<PERSON><PERSON>n", false, 2377008741735888723], [1967864351173319501, "muda", false, 378558405660086713], [2013030631243296465, "webview2_com", false, 6644751358008741449], [3150220818285335163, "url", false, 11858171219582627557], [3331586631144870129, "getrandom", false, 8722872101223450065], [4143744114649553716, "raw_window_handle", false, 2607158835357174031], [4919829919303820331, "serialize_to_javascript", false, 14677713928541049205], [5986029879202738730, "log", false, 1541928973590822586], [8569119365930580996, "serde_json", false, 13240143706165462653], [9010263965687315507, "http", false, 2011574105913395122], [9689903380558560274, "serde", false, 18218662921607851820], [10229185211513642314, "mime", false, 8620810935686852553], [10806645703491011684, "thiserror", false, 14768882660834217619], [11989259058781683633, "dunce", false, 15281095910620167697], [12092653563678505622, "build_script_build", false, 14393040121744108566], [12304025191202589669, "tauri_runtime_wry", false, 4870953348904184400], [12565293087094287914, "window_vibrancy", false, 2089277417159280179], [12943761728066819757, "tauri_runtime", false, 3838710082321182310], [12944427623413450645, "tokio", false, 10790795459829226062], [12986574360607194341, "serde_repr", false, 13097580760992055487], [13077543566650298139, "heck", false, 9633679607406152651], [13405681745520956630, "tauri_macros", false, 202075761878083440], [13625485746686963219, "anyhow", false, 5958319405184604132], [14585479307175734061, "windows", false, 5984141677745590645], [16928111194414003569, "dirs", false, 7431422511267202998], [17155886227862585100, "glob", false, 12797762323271349913]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-463a098010ccb00d\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
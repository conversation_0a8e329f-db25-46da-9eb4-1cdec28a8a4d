# Test MCP endpoints
$baseUrl = "http://127.0.0.1:8080"

Write-Host "Testing MCP Feedback Tool SSE Server" -ForegroundColor Green
Write-Host "Base URL: $baseUrl" -ForegroundColor Cyan
Write-Host ""

# Test 1: Health check
Write-Host "1. Testing health check..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get
    Write-Host "Health check response:" -ForegroundColor Green
    $healthResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Health check failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test 2: Initialize
Write-Host "2. Testing MCP initialize..." -ForegroundColor Yellow
$initRequest = @{
    jsonrpc = "2.0"
    id = 1
    method = "initialize"
    params = @{
        protocolVersion = "2024-11-05"
        capabilities = @{}
        clientInfo = @{
            name = "test"
            version = "1.0.0"
        }
    }
} | ConvertTo-Json -Depth 3

try {
    $initResponse = Invoke-RestMethod -Uri "$baseUrl/mcp" -Method Post -Body $initRequest -ContentType "application/json"
    Write-Host "Initialize response:" -ForegroundColor Green
    $initResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Initialize failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test 3: Tools list
Write-Host "3. Testing tools list..." -ForegroundColor Yellow
$toolsRequest = @{
    jsonrpc = "2.0"
    id = 2
    method = "tools/list"
    params = @{}
} | ConvertTo-Json -Depth 3

try {
    $toolsResponse = Invoke-RestMethod -Uri "$baseUrl/mcp" -Method Post -Body $toolsRequest -ContentType "application/json"
    Write-Host "Tools list response:" -ForegroundColor Green
    $toolsResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Tools list failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test 4: Call feedback tool
Write-Host "4. Testing feedback tool call..." -ForegroundColor Yellow
$feedbackRequest = @{
    jsonrpc = "2.0"
    id = 3
    method = "tools/call"
    params = @{
        name = "feedback"
        arguments = @{
            work_summary = "## SSE测试内容`n这是一个通过SSE模式测试的MCP工具调用"
            title = "SSE测试会话"
            allow_save = $true
        }
    }
} | ConvertTo-Json -Depth 3

try {
    $feedbackResponse = Invoke-RestMethod -Uri "$baseUrl/mcp" -Method Post -Body $feedbackRequest -ContentType "application/json"
    Write-Host "Feedback tool response:" -ForegroundColor Green
    $feedbackResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Feedback tool failed: $_" -ForegroundColor Red
}
Write-Host ""

Write-Host "MCP endpoint testing completed!" -ForegroundColor Green

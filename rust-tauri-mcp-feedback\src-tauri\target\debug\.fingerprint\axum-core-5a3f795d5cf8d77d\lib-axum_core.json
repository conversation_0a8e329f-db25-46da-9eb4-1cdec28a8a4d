{"rustc": 12488743700189009532, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 15657897354478470176, "path": 15702981732299530514, "deps": [[784494742817713399, "tower_service", false, 13479107168776083661], [1906322745568073236, "pin_project_lite", false, 16126799307280163106], [2517136641825875337, "sync_wrapper", false, 3332081070032233106], [7712452662827335977, "tower_layer", false, 4089728681624888718], [7858942147296547339, "rustversion", false, 13526780947066300601], [8606274917505247608, "tracing", false, 3597496046565943423], [9010263965687315507, "http", false, 2011574105913395122], [10229185211513642314, "mime", false, 8620810935686852553], [10629569228670356391, "futures_util", false, 2064565491164891640], [11946729385090170470, "async_trait", false, 16758501356086915741], [14084095096285906100, "http_body", false, 2493371637639160754], [16066129441945555748, "bytes", false, 7304204668079407586], [16900715236047033623, "http_body_util", false, 11804376373189318224]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-5a3f795d5cf8d77d\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
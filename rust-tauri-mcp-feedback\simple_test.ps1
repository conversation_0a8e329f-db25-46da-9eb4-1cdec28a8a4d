# Simple MCP test
Write-Host "Testing MCP SSE Server" -ForegroundColor Green

# Test initialize
$initData = @{
    jsonrpc = "2.0"
    id = 1
    method = "initialize"
    params = @{
        protocolVersion = "2024-11-05"
        capabilities = @{}
        clientInfo = @{
            name = "test"
            version = "1.0.0"
        }
    }
}

$json = $initData | ConvertTo-Json -Depth 5
Write-Host "Sending initialize request..." -ForegroundColor Yellow
Write-Host $json -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:8080/mcp" -Method Post -Body $json -ContentType "application/json"
    Write-Host "Initialize response:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

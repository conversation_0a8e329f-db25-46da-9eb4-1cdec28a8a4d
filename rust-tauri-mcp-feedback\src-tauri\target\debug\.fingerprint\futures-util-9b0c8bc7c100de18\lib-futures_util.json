{"rustc": 12488743700189009532, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 5251986255978778699, "deps": [[5103565458935487, "futures_io", false, 7254799124565121960], [1615478164327904835, "pin_utils", false, 7700360187348256329], [1811549171721445101, "futures_channel", false, 13156570865159914785], [1906322745568073236, "pin_project_lite", false, 16126799307280163106], [5451793922601807560, "slab", false, 7125423198499086078], [7013762810557009322, "futures_sink", false, 16190572654118984067], [7620660491849607393, "futures_core", false, 13312733302706390815], [10565019901765856648, "futures_macro", false, 12291272707250466544], [15932120279885307830, "memchr", false, 9639641664727124646], [16240732885093539806, "futures_task", false, 3357989597671250158]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-9b0c8bc7c100de18\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
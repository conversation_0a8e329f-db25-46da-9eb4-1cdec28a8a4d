{"rustc": 12488743700189009532, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 4497968502772010357, "deps": [[376837177317575824, "softbuffer", false, 11413546876017387776], [654232091421095663, "tauri_utils", false, 14871906533553561277], [2013030631243296465, "webview2_com", false, 6644751358008741449], [3150220818285335163, "url", false, 11858171219582627557], [3722963349756955755, "once_cell", false, 5781271793034976311], [4143744114649553716, "raw_window_handle", false, 2607158835357174031], [5986029879202738730, "log", false, 1541928973590822586], [8826339825490770380, "tao", false, 17606873391828522676], [9010263965687315507, "http", false, 2011574105913395122], [9141053277961803901, "wry", false, 1178613909935683121], [12304025191202589669, "build_script_build", false, 7782591809648873705], [12943761728066819757, "tauri_runtime", false, 3838710082321182310], [14585479307175734061, "windows", false, 5984141677745590645]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-3575feee3c663c04\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
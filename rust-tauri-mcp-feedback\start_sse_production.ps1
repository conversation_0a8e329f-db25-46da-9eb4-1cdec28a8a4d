# MCP Feedback Tool - Production SSE Server Startup Script
param(
    [string]$Host = "127.0.0.1",
    [int]$Port = 8080,
    [switch]$Debug,
    [switch]$Help
)

if ($Help) {
    Write-Host "MCP Feedback Tool SSE服务器启动脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法:"
    Write-Host "  .\start_sse_production.ps1                    # 启动生产版本"
    Write-Host "  .\start_sse_production.ps1 -Debug            # 启动调试版本"
    Write-Host "  .\start_sse_production.ps1 -Host 0.0.0.0     # 监听所有接口"
    Write-Host "  .\start_sse_production.ps1 -Port 9000        # 自定义端口"
    Write-Host ""
    Write-Host "端点说明:"
    Write-Host "  健康检查: http://$Host`:$Port/health"
    Write-Host "  SSE事件流: http://$Host`:$Port/sse"
    Write-Host "  MCP协议: http://$Host`:$Port/mcp"
    exit 0
}

$ErrorActionPreference = "Stop"

# 设置工作目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "MCP Feedback Tool SSE服务器" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""

# 设置环境变量
$env:MCP_TRANSPORT = "sse"
$env:MCP_HOST = $Host
$env:MCP_PORT = $Port
if ($Debug) {
    $env:RUST_LOG = "debug"
    $ExePath = ".\src-tauri\target\debug\mcp_server.exe"
    Write-Host "模式: Debug" -ForegroundColor Yellow
} else {
    $env:RUST_LOG = "info"
    $ExePath = ".\src-tauri\target\release\mcp_server.exe"
    Write-Host "模式: Release" -ForegroundColor Green
}

Write-Host "配置信息:" -ForegroundColor Cyan
Write-Host "  传输模式: SSE (Server-Sent Events)" -ForegroundColor Cyan
Write-Host "  服务器地址: http://$Host`:$Port" -ForegroundColor Cyan
Write-Host "  日志级别: $env:RUST_LOG" -ForegroundColor Cyan
Write-Host ""

Write-Host "可用端点:" -ForegroundColor Yellow
Write-Host "  健康检查: http://$Host`:$Port/health" -ForegroundColor Yellow
Write-Host "  SSE事件流: http://$Host`:$Port/sse" -ForegroundColor Yellow
Write-Host "  MCP协议: http://$Host`:$Port/mcp" -ForegroundColor Yellow
Write-Host ""

# 检查可执行文件
if (-not (Test-Path $ExePath)) {
    Write-Host "可执行文件不存在，正在构建..." -ForegroundColor Yellow
    Set-Location "src-tauri"
    try {
        if ($Debug) {
            cargo build --bin mcp_server
        } else {
            cargo build --release --bin mcp_server
        }
        Set-Location ".."
        Write-Host "构建完成!" -ForegroundColor Green
    } catch {
        Write-Host "构建失败: $_" -ForegroundColor Red
        Set-Location ".."
        exit 1
    }
}

Write-Host "启动服务器..." -ForegroundColor Green
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host ""

try {
    & $ExePath
} catch {
    Write-Host "服务器启动失败: $_" -ForegroundColor Red
    exit 1
}

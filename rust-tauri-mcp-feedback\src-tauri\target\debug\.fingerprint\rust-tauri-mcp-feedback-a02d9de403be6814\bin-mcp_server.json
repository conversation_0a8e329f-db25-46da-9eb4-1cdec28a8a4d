{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 15465163654522617972, "profile": 8731458305071235362, "path": 4232419547062613250, "deps": [[2706460456408817945, "futures", false, 9520438760689628323], [3601586811267292532, "tower", false, 2863708501462792536], [3722963349756955755, "once_cell", false, 5781271793034976311], [4891297352905791595, "axum", false, 11445625587490934039], [5986029879202738730, "log", false, 1541928973590822586], [6898646762435821041, "env_logger", false, 13334915887421750311], [8319709847752024821, "uuid", false, 713660612388245101], [8569119365930580996, "serde_json", false, 13240143706165462653], [9388111363236443182, "rust_tauri_mcp_feedback_lib", false, 5494438400935749993], [9388111363236443182, "build_script_build", false, 11081045755287560913], [9689903380558560274, "serde", false, 18218662921607851820], [9897246384292347999, "chrono", false, 12946171553064385828], [11957360342995674422, "hyper", false, 9692431086755136066], [12092653563678505622, "tauri", false, 14326423088208899670], [12944427623413450645, "tokio", false, 10790795459829226062], [13625485746686963219, "anyhow", false, 5958319405184604132], [14435908599267459652, "tower_http", false, 6938275762931589907], [15299814984394074821, "rusqlite", false, 4375678516402119540], [16702348383442838006, "tauri_plugin_opener", false, 6193246454350848309]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rust-tauri-mcp-feedback-a02d9de403be6814\\dep-bin-mcp_server", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
["\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\clear_all_browsing_data.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\create_webview.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\create_webview_window.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\get_all_webviews.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\internal_toggle_devtools.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\print.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\reparent.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_auto_resize.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_background_color.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_focus.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_position.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_size.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_zoom.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\webview_close.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\webview_hide.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\webview_position.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\webview_show.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\commands\\webview_size.toml", "\\\\?\\F:\\mcp\\Rustmcp\\rust-tauri-mcp-feedback\\src-tauri\\target\\debug\\build\\tauri-8ceb79a73f0db904\\out\\permissions\\webview\\autogenerated\\default.toml"]
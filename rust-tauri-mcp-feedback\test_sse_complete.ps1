# Complete SSE Mode Testing Script
param(
    [string]$Host = "127.0.0.1",
    [int]$Port = 8080
)

$baseUrl = "http://$Host`:$Port"

Write-Host "MCP Feedback Tool SSE模式完整测试" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "服务器地址: $baseUrl" -ForegroundColor Cyan
Write-Host ""

# Test 1: Health Check
Write-Host "1. 健康检查测试..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get
    Write-Host "✅ 健康检查成功" -ForegroundColor Green
    Write-Host "   服务: $($healthResponse.service)" -ForegroundColor Gray
    Write-Host "   状态: $($healthResponse.status)" -ForegroundColor Gray
    Write-Host "   版本: $($healthResponse.version)" -ForegroundColor Gray
} catch {
    Write-Host "❌ 健康检查失败: $_" -ForegroundColor Red
    exit 1
}
Write-Host ""

# Test 2: MCP Initialize
Write-Host "2. MCP初始化测试..." -ForegroundColor Yellow
$initRequest = @{
    jsonrpc = "2.0"
    id = 1
    method = "initialize"
    params = @{
        protocolVersion = "2024-11-05"
        capabilities = @{}
        clientInfo = @{
            name = "SSE-Test-Client"
            version = "1.0.0"
        }
    }
} | ConvertTo-Json -Depth 5

try {
    $initResponse = Invoke-RestMethod -Uri "$baseUrl/mcp" -Method Post -Body $initRequest -ContentType "application/json"
    if ($initResponse.error) {
        Write-Host "❌ 初始化失败: $($initResponse.error.message)" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ MCP初始化成功" -ForegroundColor Green
    Write-Host "   协议版本: $($initResponse.result.protocolVersion)" -ForegroundColor Gray
    Write-Host "   服务器: $($initResponse.result.serverInfo.name) v$($initResponse.result.serverInfo.version)" -ForegroundColor Gray
} catch {
    Write-Host "❌ 初始化失败: $_" -ForegroundColor Red
    exit 1
}
Write-Host ""

# Test 3: Tools List
Write-Host "3. 工具列表测试..." -ForegroundColor Yellow
$toolsRequest = @{
    jsonrpc = "2.0"
    id = 2
    method = "tools/list"
    params = @{}
} | ConvertTo-Json -Depth 5

try {
    $toolsResponse = Invoke-RestMethod -Uri "$baseUrl/mcp" -Method Post -Body $toolsRequest -ContentType "application/json"
    if ($toolsResponse.error) {
        Write-Host "❌ 工具列表获取失败: $($toolsResponse.error.message)" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ 工具列表获取成功" -ForegroundColor Green
    $tools = $toolsResponse.result.tools
    Write-Host "   可用工具数量: $($tools.Count)" -ForegroundColor Gray
    foreach ($tool in $tools) {
        Write-Host "   - $($tool.name): $($tool.description)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 工具列表获取失败: $_" -ForegroundColor Red
    exit 1
}
Write-Host ""

# Test 4: SSE Connection Test
Write-Host "4. SSE连接测试..." -ForegroundColor Yellow
try {
    # 简单的SSE连接测试
    $sseTest = Start-Job -ScriptBlock {
        param($url)
        try {
            $response = Invoke-WebRequest -Uri $url -TimeoutSec 5
            return $response.StatusCode
        } catch {
            return $_.Exception.Message
        }
    } -ArgumentList "$baseUrl/sse"
    
    $result = Wait-Job $sseTest -Timeout 10 | Receive-Job
    Remove-Job $sseTest -Force
    
    if ($result -eq 200) {
        Write-Host "✅ SSE端点连接成功" -ForegroundColor Green
    } else {
        Write-Host "⚠️  SSE端点测试: $result" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  SSE连接测试失败: $_" -ForegroundColor Yellow
}
Write-Host ""

Write-Host "🎉 SSE模式测试完成!" -ForegroundColor Green
Write-Host ""
Write-Host "配置信息:" -ForegroundColor Cyan
Write-Host "  健康检查: $baseUrl/health" -ForegroundColor Cyan
Write-Host "  SSE事件流: $baseUrl/sse" -ForegroundColor Cyan
Write-Host "  MCP协议: $baseUrl/mcp" -ForegroundColor Cyan
Write-Host ""
Write-Host "Augment Code配置:" -ForegroundColor Cyan
Write-Host @"
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "feedback-sse",
        "transport": {
          "type": "sse",
          "url": "$baseUrl/mcp",
          "eventSource": "$baseUrl/sse"
        },
        "env": {
          "RUST_LOG": "info"
        }
      }
    ]
  }
}
"@ -ForegroundColor Gray

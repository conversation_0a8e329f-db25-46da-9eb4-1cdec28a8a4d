{"rustc": 12488743700189009532, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 3239258203469814694, "deps": [[1988483478007900009, "unicode_ident", false, 1503458344155500476], [3060637413840920116, "proc_macro2", false, 10216015943829803987], [17990358020177143287, "quote", false, 9619711520884181919]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-43fd0f87d34f2a6b\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
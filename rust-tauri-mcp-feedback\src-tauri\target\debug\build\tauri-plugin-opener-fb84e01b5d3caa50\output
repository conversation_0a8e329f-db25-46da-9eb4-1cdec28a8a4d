cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=F:\mcp\Rustmcp\rust-tauri-mcp-feedback\src-tauri\target\debug\build\tauri-plugin-opener-fb84e01b5d3caa50\out\tauri-plugin-opener-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=F:\mcp\Rustmcp\rust-tauri-mcp-feedback\src-tauri\target\debug\build\tauri-plugin-opener-fb84e01b5d3caa50\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tauri-plugin-opener-2.4.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)

{"rustc": 12488743700189009532, "features": "[\"cors\", \"default\", \"trace\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 15657897354478470176, "path": 16477821813721994333, "deps": [[784494742817713399, "tower_service", false, 13479107168776083661], [1906322745568073236, "pin_project_lite", false, 16126799307280163106], [7712452662827335977, "tower_layer", false, 4089728681624888718], [7896293946984509699, "bitflags", false, 7537122399998279723], [8606274917505247608, "tracing", false, 3597496046565943423], [9010263965687315507, "http", false, 2011574105913395122], [14084095096285906100, "http_body", false, 2493371637639160754], [16066129441945555748, "bytes", false, 7304204668079407586], [16900715236047033623, "http_body_util", false, 11804376373189318224]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-8b53573c706056e6\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}